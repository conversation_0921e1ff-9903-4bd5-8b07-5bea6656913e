local Fluent = loadstring(game:HttpGet("https://github.com/dawid-scripts/Fluent/releases/latest/download/main.lua"))()
local SaveManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/SaveManager.lua"))()
local InterfaceManager = loadstring(game:HttpGet("https://raw.githubusercontent.com/dawid-scripts/Fluent/master/Addons/InterfaceManager.lua"))()
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local GameEvents = ReplicatedStorage.GameEvents
local Window = Fluent:CreateWindow({
    Title = "Fluent " .. Fluent.Version,
    SubTitle = "by XZery",
    TabWidth = 160,
    Size = UDim2.fromOffset(580, 460),
    Acrylic = true, -- The blur may be detectable, setting this to false disables blur entirely
    Theme = "Dark",
    MinimizeKey = Enum.KeyCode.LeftControl -- Used when theres no MinimizeKeybind
})

--Fluent provides Lucide Icons https://lucide.dev/icons/ for the tabs, icons are optional
local Tabs = {
    Main = Window:AddTab({ Title = "Main", Icon = "" }),
    Seeds = Window:AddTab({ Title = "Seeds", Icon = "sprout" }),
    Settings = Window:AddTab({ Title = "Settings", Icon = "settings" })
}

local Options = Fluent.Options

-- Seed Stock functionality
local LocalPlayer = game.Players.LocalPlayer
local PlayerGui = LocalPlayer.PlayerGui
local SeedStock = {}

-- All available seeds in the game (will be populated from GetSeedStock)
local AllSeeds = {}

-- Selected seeds for auto-buy
local SelectedSeeds = {}
local SelectedSeedStock = {Selected = {}}

local function GetSeedStock(IgnoreNoStock: boolean?): table
	local SeedShop = PlayerGui.Seed_Shop
	local Items = SeedShop:FindFirstChild("Blueberry", true).Parent

	local NewList = {}

	for _, Item in next, Items:GetChildren() do
		local MainFrame = Item:FindFirstChild("Main_Frame")
		if not MainFrame then continue end

		local StockText = MainFrame.Stock_Text.Text
		local StockCount = tonumber(StockText:match("%d+"))

		--// Seperate list
		if IgnoreNoStock then
			if StockCount <= 0 then continue end
			NewList[Item.Name] = StockCount
			continue
		end

		SeedStock[Item.Name] = StockCount
	end

	return IgnoreNoStock and NewList or SeedStock
end

local function BuySeed(Seed: string)
	GameEvents.BuySeedStock:FireServer(Seed)
end

local function BuyAllSelectedSeeds()
    for _, seedName in pairs(SelectedSeedStock.Selected) do
        local Stock = SeedStock[seedName]

        if Stock and Stock > 0 then
            for i = 1, Stock do
                BuySeed(seedName)
            end
        end
    end
end

local function updateAllSeeds()
    local success, result = pcall(function()
        local allStockData = GetSeedStock(false) -- Get all seeds including 0 stock
        local newAllSeeds = {}

        for seedName, count in pairs(allStockData) do
            table.insert(newAllSeeds, seedName)
        end

        -- Sort alphabetically
        table.sort(newAllSeeds)

        -- Check if AllSeeds changed
        local hasChanged = #AllSeeds ~= #newAllSeeds
        if not hasChanged then
            for i, seed in ipairs(newAllSeeds) do
                if AllSeeds[i] ~= seed then
                    hasChanged = true
                    break
                end
            end
        end

        -- Update AllSeeds if changed
        if hasChanged then
            AllSeeds = newAllSeeds
            return true
        end

        return false
    end)

    return success and result
end

-- Seeds Tab Content
local seedStockParagraph = Tabs.Seeds:AddParagraph({
    Title = "Available Seeds (Auto-Refresh)",
    Content = "Loading seed information..."
})

-- Initialize AllSeeds first
local seedDropdown = nil
local function initializeSeeds()
    local success = updateAllSeeds()
    if success and #AllSeeds > 0 then
        -- Set default selected seeds (empty table)
        SelectedSeedStock.Selected = {}

        -- Update dropdown if it exists
        if seedDropdown then
            seedDropdown:SetValues(AllSeeds)
            seedDropdown:SetValue({}) -- Start with no selection
        end

        return true
    end
    return false
end

-- Auto-Buy Settings
local autoBuyEnabled = false
local autoBuyToggle = Tabs.Seeds:AddToggle("AutoBuyToggle", {
    Title = "Auto-Buy Seeds",
    Description = "Automatically buy selected seeds when restocked",
    Default = false
})

autoBuyToggle:OnChanged(function(Value)
    autoBuyEnabled = Value
end)

-- Seed Selection Dropdown (will be populated after initialization)
seedDropdown = Tabs.Seeds:AddDropdown("SeedDropdown", {
    Title = "Select Seeds to Auto-Buy",
    Description = "Choose which seeds to buy automatically (Loading...)",
    Values = {"Loading..."},
    Multi = true,
    Default = {},
})

seedDropdown:OnChanged(function(Value)
    SelectedSeedStock.Selected = Value
end)

-- Manual Buy Button
local buyButton = Tabs.Seeds:AddButton({
    Title = "Buy Selected Seeds (All Stock)",
    Description = "Buy all available stock of selected seeds",
    Callback = function()
        BuyAllSelectedSeeds()
    end
})



local lastStockData = {}
local stockConnection = nil
local seedsInitialized = false

local function updateSeedStock()
    local success, result = pcall(function()
        -- Initialize seeds if not done yet
        if not seedsInitialized then
            if initializeSeeds() then
                seedsInitialized = true
                seedDropdown:SetDesc("Choose which seeds to buy automatically")
            end
        end

        -- Update AllSeeds periodically in case new seeds are added
        if seedsInitialized then
            local seedsUpdated = updateAllSeeds()
            if seedsUpdated then
                seedDropdown:SetValues(AllSeeds)
                -- Keep current selections that still exist
                local newSelection = {}
                for _, selectedSeed in pairs(SelectedSeedStock.Selected) do
                    if table.find(AllSeeds, selectedSeed) then
                        table.insert(newSelection, selectedSeed)
                    end
                end
                SelectedSeedStock.Selected = newSelection
                seedDropdown:SetValue(newSelection)
            end
        end

        local stockData = GetSeedStock(true) -- Only get available stock
        local stockText = "Available Seeds:\n\n"

        -- Check if stock data changed
        local hasChanged = false
        for seedName, count in pairs(stockData) do
            if lastStockData[seedName] ~= count then
                hasChanged = true
                break
            end
        end

        -- Check if any seed was removed
        for seedName, count in pairs(lastStockData) do
            if stockData[seedName] == nil then
                hasChanged = true
                break
            end
        end

        -- Update display if changed or auto-buy
        if hasChanged then
            -- Check for auto-buy
            if autoBuyEnabled and SelectedSeedStock.Selected and #SelectedSeedStock.Selected > 0 then
                local shouldBuy = false
                for _, selectedSeed in pairs(SelectedSeedStock.Selected) do
                    if stockData[selectedSeed] and stockData[selectedSeed] > 0 then
                        -- Check if this seed just restocked (wasn't available before or count increased)
                        if not lastStockData[selectedSeed] or lastStockData[selectedSeed] == 0 or stockData[selectedSeed] > lastStockData[selectedSeed] then
                            shouldBuy = true
                            break
                        end
                    end
                end

                if shouldBuy then
                    BuyAllSelectedSeeds()
                end
            end

            lastStockData = stockData

            for seedName, count in pairs(stockData) do
                stockText = stockText .. seedName .. ": " .. count .. "\n"
            end

            if stockText == "Available Seeds:\n\n" then
                stockText = "No seeds in stock!"
            end

            seedStockParagraph:SetDesc(stockText)
        end


    end)

    if not success then
        seedStockParagraph:SetDesc("Seed Shop not accessible. Please open Seed Shop!")
        if not seedsInitialized then
            seedDropdown:SetDesc("Open Seed Shop to load seeds")
        end
    end
end

-- Start auto-refresh
stockConnection = game:GetService("RunService").Heartbeat:Connect(function()
    updateSeedStock()
end)

    local speed = Tabs.Main:AddSlider("Slider", {
        Title = "Speed",
        Description = "Set Your Speed",
        Default = 16,
        Min = 16,
        Max = 200,
        Rounding = 1,
    })

    speed:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.WalkSpeed = Value
    end)

    speed:SetValue(16)

    local jump = Tabs.Main:AddSlider("Slider", {
        Title = "jump",
        Description = "Set Your JumpPower",
        Default = 50,
        Min = 50,
        Max = 200,
        Rounding = 1,
    })

    jump:OnChanged(function(Value)
        local player = game.Players.LocalPlayer
        local char = player.Character
        char.Humanoid.JumpPower = Value
    end)

    jump:SetValue(50)

    -- Fly toggle functionality
    local flyEnabled = false
    local bodyVelocity = nil
    local bodyAngularVelocity = nil
    local connection = nil

    local function enableFly()
        local player = game.Players.LocalPlayer
        local char = player.Character
        if not char or not char:FindFirstChild("HumanoidRootPart") then return end

        local humanoidRootPart = char.HumanoidRootPart

        -- Create BodyVelocity for movement
        bodyVelocity = Instance.new("BodyVelocity")
        bodyVelocity.MaxForce = Vector3.new(9e9, 9e9, 9e9)
        bodyVelocity.Velocity = Vector3.new(0, 0, 0)
        bodyVelocity.Parent = humanoidRootPart

        -- Create BodyAngularVelocity for rotation control
        bodyAngularVelocity = Instance.new("BodyAngularVelocity")
        bodyAngularVelocity.MaxTorque = Vector3.new(0, 9e9, 0)
        bodyAngularVelocity.AngularVelocity = Vector3.new(0, 0, 0)
        bodyAngularVelocity.Parent = humanoidRootPart

        -- Movement control
        connection = game:GetService("RunService").Heartbeat:Connect(function()
            local camera = workspace.CurrentCamera
            local userInputService = game:GetService("UserInputService")

            local velocity = Vector3.new(0, 0, 0)
            local flySpeed = 50

            -- Get camera directions
            local lookDirection = camera.CFrame.LookVector
            local rightDirection = camera.CFrame.RightVector
            local upDirection = Vector3.new(0, 1, 0)

            -- Simple directional movement based on camera
            if userInputService:IsKeyDown(Enum.KeyCode.W) then
                velocity = velocity + lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.S) then
                velocity = velocity - lookDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.A) then
                velocity = velocity - rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.D) then
                velocity = velocity + rightDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.Space) then
                velocity = velocity + upDirection * flySpeed
            end
            if userInputService:IsKeyDown(Enum.KeyCode.LeftShift) then
                velocity = velocity - upDirection * flySpeed
            end

            bodyVelocity.Velocity = velocity
        end)
    end

    local function disableFly()
        if bodyVelocity then
            bodyVelocity:Destroy()
            bodyVelocity = nil
        end
        if bodyAngularVelocity then
            bodyAngularVelocity:Destroy()
            bodyAngularVelocity = nil
        end
        if connection then
            connection:Disconnect()
            connection = nil
        end
    end

    local flyToggle = Tabs.Main:AddToggle("FlyToggle", {
        Title = "Fly",
        Description = "Toggle fly mode",
        Default = false
    })

    flyToggle:OnChanged(function(Value)
        flyEnabled = Value
        if flyEnabled then
            enableFly()
        else
            disableFly()
        end
    end)

    -- Reset Character Button
    local resetButton = Tabs.Main:AddButton({
        Title = "Reset Character",
        Description = "Reset your character",
        Callback = function()
            local player = game.Players.LocalPlayer
            if player.Character then
                player.Character:BreakJoints()
            end
        end
    })

    -- Anti-AFK functionality
    local antiAfkEnabled = false
    local antiAfkConnection = nil
    local lastMoveTime = 0
    local isMoving = false
    local moveStartTime = 0

    local function startAntiAfk()
        antiAfkConnection = game:GetService("RunService").Heartbeat:Connect(function()
            if not antiAfkEnabled then return end

            local currentTime = tick()
            local player = game.Players.LocalPlayer
            local char = player.Character

            if char and char:FindFirstChild("Humanoid") and char:FindFirstChild("HumanoidRootPart") then
                local humanoid = char.Humanoid
                local rootPart = char.HumanoidRootPart

                -- Check if it's time to move (every 30 seconds)
                if not isMoving and (currentTime - lastMoveTime) >= 30 then
                    -- Start moving
                    isMoving = true
                    moveStartTime = currentTime
                    lastMoveTime = currentTime

                    -- Generate random direction
                    local randomX = math.random(-10, 10)
                    local randomZ = math.random(-10, 10)
                    local randomDirection = Vector3.new(randomX, 0, randomZ).Unit

                    -- Move in random direction
                    local targetPosition = rootPart.Position + (randomDirection * 5)
                    humanoid:MoveTo(targetPosition)
                end

                -- Stop moving after 2 seconds
                if isMoving and (currentTime - moveStartTime) >= 2 then
                    isMoving = false
                    humanoid:MoveTo(rootPart.Position) -- Stop at current position
                end
            end
        end)
    end

    local function stopAntiAfk()
        if antiAfkConnection then
            antiAfkConnection:Disconnect()
            antiAfkConnection = nil
        end
    end

    local antiAfkToggle = Tabs.Main:AddToggle("AntiAfkToggle", {
        Title = "Anti-AFK",
        Description = "Auto walk randomly every 30 seconds",
        Default = false
    })

    antiAfkToggle:OnChanged(function(Value)
        antiAfkEnabled = Value
        if antiAfkEnabled then
            startAntiAfk()
        else
            stopAntiAfk()
        end
    end)

-- Addons:
-- SaveManager (Allows you to have a configuration system)
-- InterfaceManager (Allows you to have a interface managment system)

-- Hand the library over to our managers
SaveManager:SetLibrary(Fluent)
InterfaceManager:SetLibrary(Fluent)

-- Ignore keys that are used by ThemeManager.
-- (we dont want configs to save themes, do we?)
SaveManager:IgnoreThemeSettings()

-- You can add indexes of elements the save manager should ignore
SaveManager:SetIgnoreIndexes({})

-- use case for doing it this way:
-- a script hub could have themes in a global folder
-- and game configs in a separate folder per game
InterfaceManager:SetFolder("FluentScriptHub")
SaveManager:SetFolder("FluentScriptHub/specific-game")

InterfaceManager:BuildInterfaceSection(Tabs.Settings)
SaveManager:BuildConfigSection(Tabs.Settings)


Window:SelectTab(1)

Fluent:Notify({
    Title = "Fluent",
    Content = "The script has been loaded.",
    Duration = 8
})

-- You can use the SaveManager:LoadAutoloadConfig() to load a config
-- which has been marked to be one that auto loads!
SaveManager:LoadAutoloadConfig()